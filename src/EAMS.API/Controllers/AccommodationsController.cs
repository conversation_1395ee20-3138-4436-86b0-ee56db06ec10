using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Identity.Web.Resource;
using EAMS.API.DTOs;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Entities;
using EAMS.Domain.Entities.Enums;
using AutoMapper;
using NetTopologySuite.Geometries;
using EAMS.API.Filters;

namespace EAMS.API.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class AccommodationsController : ControllerBase
{
    private readonly IAccommodationService _accommodationService;
    private readonly ILogger<AccommodationsController> _logger;
    private readonly IMapper _mapper;

    public AccommodationsController(
        IAccommodationService accommodationService,
        ILogger<AccommodationsController> logger,
        IMapper mapper)
    {
        _accommodationService = accommodationService;
        _logger = logger;
        _mapper = mapper;
    }

    /// <summary>
    /// Get all accommodations with optional filtering
    /// </summary>
    [HttpGet]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes:Read")]
    [Authorize(Roles = "Users, Managers")]
    public async Task<ActionResult<IEnumerable<AccommodationDto>>> GetAccommodations()
    {
        var accommodations = await _accommodationService.GetAllAccommodationsAsync();
        var response = _mapper.Map<IEnumerable<AccommodationDto>>(accommodations);
        return Ok(response);
    }

    /// <summary>
    /// Get accommodation by ID
    /// </summary>
    [HttpGet("{id}")]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes:Read")]
    [Authorize(Roles = "Users, Managers")]
    public async Task<ActionResult<AccommodationDto>> GetAccommodation(Int64 id)
    {
        var accommodation = await _accommodationService.GetAccommodationByIdAsync(id);

        if (accommodation == null)
        {
            return NotFound($"Accommodation with ID {id} not found");
        }

        var response = _mapper.Map<AccommodationDto>(accommodation);
        return Ok(response);
    }

    /// <summary>
    /// Create a new accommodation
    /// </summary>
    [HttpPost]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes:Write")]
    [Authorize(Roles = "Managers")]
    [ModelStateValidationFilter]
    public async Task<ActionResult<AccommodationDto>> CreateAccommodation(AccommodationDto accommodationDto)
    {
        var accommodation = _mapper.Map<Accommodation>(accommodationDto);
        var createdAccommodation = await _accommodationService.CreateAccommodationAsync(accommodation);
        var response = _mapper.Map<AccommodationDto>(createdAccommodation);

        return CreatedAtAction(nameof(GetAccommodation), new { id = createdAccommodation.Id }, response);
    }

    /// <summary>
    /// Update an existing accommodation
    /// </summary>
    [HttpPut("{id}")]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes:Write")]
    [Authorize(Roles = "Managers")]
    [ModelStateValidationFilter]
    public async Task<ActionResult<AccommodationDto>> UpdateAccommodation(Int64 id, AccommodationDto accommodationDto)
    {
        if (id != accommodationDto.Id)
            return BadRequest("Accommodation ID mismatch.");

        var accommodation = _mapper.Map<Accommodation>(accommodationDto);
        var updatedAccommodation = await _accommodationService.UpdateAccommodationAsync(accommodation);
        var response = _mapper.Map<AccommodationDto>(updatedAccommodation);

        return Ok(response);
    }

    /// <summary>
    /// Delete an accommodation
    /// </summary>
    [HttpDelete("{id}")]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes:Write")]
    [Authorize(Roles = "Managers")]
    public async Task<ActionResult<bool>> DeleteAccommodation(Int64 id)
    {
        var exists = await _accommodationService.AccommodationExistsAsync(id);
        if (!exists)
        {
            return NotFound(false);
        }

        var result = await _accommodationService.DeleteAccommodationAsync(id);
        return Ok(result);
    }
}
