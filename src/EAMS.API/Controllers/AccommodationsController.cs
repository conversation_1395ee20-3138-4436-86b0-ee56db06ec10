using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Identity.Web.Resource;
using EAMS.API.DTOs;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Entities;
using EAMS.Domain.Entities.Enums;
using AutoMapper;
using NetTopologySuite.Geometries;

namespace EAMS.API.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class AccommodationsController : ControllerBase
{
    private readonly IAccommodationService _accommodationService;
    private readonly ILogger<AccommodationsController> _logger;
    private readonly IMapper _mapper;

    public AccommodationsController(
        IAccommodationService accommodationService,
        ILogger<AccommodationsController> logger,
        IMapper mapper)
    {
        _accommodationService = accommodationService;
        _logger = logger;
        _mapper = mapper;
    }

    /// <summary>
    /// Get all accommodations with optional filtering
    /// </summary>
    [HttpGet]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes:Read")]
    [Authorize(Roles = "Users, Managers")]
    public async Task<ActionResult<IEnumerable<AccommodationDto>>> GetAccommodations()
    {
        try
        {
            var accommodations = await _accommodationService.GetAllAccommodationsAsync();
            var response = _mapper.Map<IEnumerable<AccommodationDto>>(accommodations);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving accommodations");
            return StatusCode(500, "An error occurred while retrieving accommodations");
        }
    }

    /// <summary>
    /// Get accommodation by ID
    /// </summary>
    [HttpGet("{id}")]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes:Read")]
    [Authorize(Roles = "Users, Managers")]
    public async Task<ActionResult<AccommodationDto>> GetAccommodation(
        Int64 id)
    {
        try
        {
            var accommodation = await _accommodationService.GetAccommodationByIdAsync(id);

            if (accommodation == null)
            {
                return NotFound($"Accommodation with ID {id} not found");
            }

            var response = _mapper.Map<AccommodationDto>(accommodation);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving accommodation with ID {Id}", id);
            return StatusCode(500, "An error occurred while retrieving the accommodation");
        }
    }

    /// <summary>
    /// Create a new accommodation
    /// </summary>
    [HttpPost]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes:Write")]
    [Authorize(Roles = "Managers")]
    public async Task<ActionResult<AccommodationDto>> CreateAccommodation(
        AccommodationDto accommodationDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var accommodation = _mapper.Map<Accommodation>(accommodationDto);
            var createdAccommodation = await _accommodationService.CreateAccommodationAsync(accommodation);
            var response = _mapper.Map<AccommodationDto>(createdAccommodation);

            return CreatedAtAction(nameof(GetAccommodation), new { id = createdAccommodation.Id }, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating accommodation");
            return StatusCode(500, "An error occurred while creating the accommodation");
        }
    }

    /// <summary>
    /// Update an existing accommodation
    /// </summary>
    [HttpPut("{id}")]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes:Write")]
    [Authorize(Roles = "Managers")]
    public async Task<ActionResult<AccommodationDto>> UpdateAccommodation(
        Int64 id,
        AccommodationDto accommodationDto)
    {
        try
        {
            if (id != accommodationDto.Id)
                return BadRequest("Accommodation ID mismatch.");

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var accommodation = _mapper.Map<Accommodation>(accommodationDto);
            var updatedAccommodation = await _accommodationService.UpdateAccommodationAsync(accommodation);
            var response = _mapper.Map<AccommodationDto>(updatedAccommodation);

            return Ok(response);
        }
        catch (InvalidOperationException ex)
        {
            if (ex.Message != null && ex.Message.ToLower().Contains("not found"))
            {
                _logger.LogWarning(ex, "Accommodation with ID {Id} not found for update", id);
                return NotFound($"Accommodation with ID {id} not found");
            }
            else
            {
                _logger.LogError(ex, "Unexpected InvalidOperationException when updating accommodation with ID {Id}", id);
                return StatusCode(500, "An error occurred while updating the accommodation");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating accommodation with ID {Id}", accommodationDto.Id);
            return StatusCode(500, "An error occurred while updating the accommodation");
        }
    }

    /// <summary>
    /// Delete an accommodation
    /// </summary>
    [HttpDelete("{id}")]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes:Write")]
    [Authorize(Roles = "Managers")]
    public async Task<ActionResult<bool>> DeleteAccommodation(
        Int64 id)
    {
        try
        {
            var exists = await _accommodationService.AccommodationExistsAsync(id);
            if (!exists)
            {
                return NotFound(false);
            }

            var result = await _accommodationService.DeleteAccommodationAsync(id);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting accommodation with ID {Id}", id);
            return StatusCode(500, "An error occurred while deleting the accommodation");
        }
    }
}
