using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Identity.Web.Resource;
using EAMS.API.DTOs;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Entities;
using EAMS.Domain.Exceptions;
using AutoMapper;
using EAMS.API.Filters;

namespace EAMS.API.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class OrganisationsController : ControllerBase
{
    private readonly IOrganisationService _organisationService;
    private readonly ILogger<OrganisationsController> _logger;
    private readonly IMapper _mapper;

    public OrganisationsController(
        IOrganisationService organisationService,
        ILogger<OrganisationsController> logger,
        IMapper mapper)
    {
        _organisationService = organisationService;
        _logger = logger;
        _mapper = mapper;
    }

    /// <summary>
    /// Get all organisations
    /// </summary>
    [HttpGet]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes:Read")]
    [Authorize(Roles = "Users, Managers")]
    public async Task<ActionResult<IEnumerable<OrganisationDto>>> GetOrganisations()
    {
        var organisations = await _organisationService.GetAllOrganisationsAsync();
        var response = _mapper.Map<IEnumerable<OrganisationDto>>(organisations);
        return Ok(response);
    }

    /// <summary>
    /// Get organisation by ID
    /// </summary>
    [HttpGet("{id}")]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes:Read")]
    [Authorize(Roles = "Users, Managers")]
    public async Task<ActionResult<OrganisationDto>> GetOrganisation(Guid id)
    {
        var organisation = await _organisationService.GetOrganisationByIdAsync(id);

        if (organisation == null)
        {
            throw new EntityNotFoundException("Organisation", id);
        }

        var response = _mapper.Map<OrganisationDto>(organisation);
        return Ok(response);
    }

    /// <summary>
    /// Create a new organisation
    /// </summary>
    [HttpPost]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes:Write")]
    [Authorize(Roles = "Managers")]
    public async Task<ActionResult<OrganisationDto>> CreateOrganisation(OrganisationDto organisationDto)
    {
        var organisation = _mapper.Map<Organisation>(organisationDto);
        var createdOrganisation = await _organisationService.CreateOrganisationAsync(organisation);
        var response = _mapper.Map<OrganisationDto>(createdOrganisation);

        return CreatedAtAction(nameof(GetOrganisation), new { id = createdOrganisation.Id }, response);
    }

    /// <summary>
    /// Update an existing organisation
    /// </summary>
    [HttpPut("{id}")]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes:Write")]
    [Authorize(Roles = "Managers")]
    [ModelStateValidationFilter]
    [IdMismatchValidationFilter]
    public async Task<ActionResult<OrganisationDto>> UpdateOrganisation(Guid id, OrganisationDto organisationDto)
    {
        var organisation = _mapper.Map<Organisation>(organisationDto);
        var updatedOrganisation = await _organisationService.UpdateOrganisationAsync(organisation);
        var response = _mapper.Map<OrganisationDto>(updatedOrganisation);

        return Ok(response);
    }

    /// <summary>
    /// Delete an organisation
    /// </summary>
    [HttpDelete("{id}")]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes:Write")]
    [Authorize(Roles = "Managers")]
    public async Task<ActionResult<bool>> DeleteOrganisation(Guid id)
    {
        var result = await _organisationService.DeleteOrganisationAsync(id);
        return Ok(result);
    }
}
