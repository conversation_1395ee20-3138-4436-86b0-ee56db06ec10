namespace EAMS.API.DTOs;

public class InvitationDto
{
    public string InvitedUserEmailAddress { get; set; } // email address of the user you are inviting
    public int TargetOrganisationId { get; set; } // Target organisation Id that user will belong to
    public string Position { get; set; }
    public string InviteRedirectUrl { get; set; } // App Url where user will be redirected in the first time login
}