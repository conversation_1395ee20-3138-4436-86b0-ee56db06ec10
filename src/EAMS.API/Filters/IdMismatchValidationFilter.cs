using EAMS.Domain.Exceptions;
using Microsoft.AspNetCore.Mvc.Filters;

namespace EAMS.API.Filters;

/// <summary>
/// Action filter that validates ID parameters match the DTO ID property
/// </summary>
public class IdMismatchValidationFilter : ActionFilterAttribute
{
    public override void OnActionExecuting(ActionExecutingContext context)
    {
        // Get the route ID parameter (could be 'id')
        if (context.ActionArguments.TryGetValue("id", out var routeId))
        {
            // Look for a DTO parameter that has an Id property
            foreach (var arg in context.ActionArguments.Values)
            {
                if (arg != null)
                {
                    var idProperty = arg.GetType().GetProperty("Id");
                    if (idProperty != null)
                    {
                        var dtoId = idProperty.GetValue(arg);
                        
                        // Compare the route ID with the DTO ID
                        if (!routeId.Equals(dtoId))
                        {
                            throw new ValidationException("Id", "The ID in the URL does not match the ID in the request body.");
                        }
                    }
                }
            }
        }

        base.OnActionExecuting(context);
    }
}
