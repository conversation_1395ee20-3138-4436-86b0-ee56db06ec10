﻿using AutoMapper;
using EAMS.API.DTOs;
using EAMS.Domain.Entities;
using GraphUser = Microsoft.Graph.Models.User;

namespace EAMS.API.Mappings
{
    public class GraphMapperProfile: Profile
    {
        public GraphMapperProfile()
        {
            CreateMap<User, UserDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.DisplayName, opt => opt.MapFrom(src => src.GraphUser.DisplayName))
                .ForMember(dest => dest.Email, opt => opt.MapFrom(src => src.GraphUser.Mail))
                .ForMember(dest => dest.GivenName, opt => opt.MapFrom(src => src.GraphUser.GivenName))
                .ForMember(dest => dest.Surname, opt => opt.MapFrom(src => src.GraphUser.Surname))
                .ForMember(dest => dest.UserPrincipalName, opt => opt.MapFrom(src => src.GraphUser.UserPrincipalName));
                    
            CreateMap<InvitationDto, UserInvitation>();
        }
    }
}
