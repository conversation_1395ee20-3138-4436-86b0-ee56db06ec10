using EAMS.API.Configurations;
using EAMS.API.Models;
using EAMS.Domain.Exceptions;
using Microsoft.Graph;
using System.Net;
using System.Text.Json;

namespace EAMS.API.Middleware;

/// <summary>
/// Global exception handling middleware that catches all unhandled exceptions
/// and returns standardized error responses
/// </summary>
public class GlobalExceptionHandlingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<GlobalExceptionHandlingMiddleware> _logger;

    public GlobalExceptionHandlingMiddleware(RequestDelegate next, ILogger<GlobalExceptionHandlingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, ICorrelationIdGenerator correlationIdGenerator)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            await HandleExceptionAsync(context, ex, correlationIdGenerator);
        }
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception exception, ICorrelationIdGenerator correlationIdGenerator)
    {
        var correlationId = correlationIdGenerator.Get();
        var errorResponse = CreateErrorResponse(exception, correlationId);

        // Log the exception with appropriate level
        LogException(exception, correlationId, context.Request.Path);

        // Set response properties
        context.Response.ContentType = "application/json";
        context.Response.StatusCode = errorResponse.StatusCode;

        // Serialize and write response
        var jsonResponse = JsonSerializer.Serialize(errorResponse, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        await context.Response.WriteAsync(jsonResponse);
    }

    private ErrorResponse CreateErrorResponse(Exception exception, string correlationId)
    {
        return exception switch
        {
            EntityNotFoundException notFoundEx => new ErrorResponse
            {
                Message = notFoundEx.Message,
                StatusCode = (int)HttpStatusCode.NotFound,
                CorrelationId = correlationId
            },
            ValidationException validationEx => new ErrorResponse
            {
                Message = validationEx.Message,
                StatusCode = (int)HttpStatusCode.BadRequest,
                CorrelationId = correlationId,
                ValidationErrors = validationEx.ValidationErrors
            },
            ConflictException conflictEx => new ErrorResponse
            {
                Message = conflictEx.Message,
                StatusCode = (int)HttpStatusCode.Conflict,
                CorrelationId = correlationId
            },
            ServiceException serviceEx => new ErrorResponse
            {
                Message = serviceEx.Message,
                StatusCode = serviceEx.ResponseStatusCode != 0 ? (int)serviceEx.ResponseStatusCode : (int)HttpStatusCode.InternalServerError,
                CorrelationId = correlationId
            },
            _ => new ErrorResponse
            {
                Message = "An unexpected error occurred",
                Details = "Please contact support if the problem persists",
                StatusCode = (int)HttpStatusCode.InternalServerError,
                CorrelationId = correlationId
            }
        };
    }

    private void LogException(Exception exception, string correlationId, string requestPath)
    {
        var logLevel = exception switch
        {
            EntityNotFoundException => LogLevel.Warning,
            ValidationException => LogLevel.Warning,
            ConflictException => LogLevel.Warning,
            ServiceException => LogLevel.Error,
            _ => LogLevel.Error
        };

        _logger.Log(logLevel, exception, 
            "Exception occurred for request {RequestPath} with correlation ID {CorrelationId}: {ExceptionType} - {Message}",
            requestPath, correlationId, exception.GetType().Name, exception.Message);
    }
}
