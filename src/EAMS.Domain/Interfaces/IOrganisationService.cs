using EAMS.Domain.Entities;

namespace EAMS.Domain.Interfaces;

public interface IOrganisationService
{
    Task<IEnumerable<Organisation>> GetAllOrganisationsAsync();
    Task<Organisation?> GetOrganisationByIdAsync(Guid id);
    Task<Organisation> CreateOrganisationAsync(Organisation organisation);
    Task<Organisation> UpdateOrganisationAsync(Organisation organisation);
    Task<bool> DeleteOrganisationAsync(Guid id);
    Task<bool> OrganisationExistsAsync(Guid id);
}
