using EAMS.Domain.Entities;
using EAMS.Domain.Interfaces;

namespace EAMS.Domain.Services;
public class AccommodationService : IAccommodationService
{
    private readonly IAccommodationRepository _accommodationRepository;

    public AccommodationService(IAccommodationRepository accommodationRepository)
    {
        _accommodationRepository = accommodationRepository;
    }

    public async Task<IEnumerable<Accommodation>> GetAllAccommodationsAsync()
    {
        return await _accommodationRepository.GetAllAsync();
    }

    public async Task<Accommodation?> GetAccommodationByIdAsync(Int64 id)
    {
        return await _accommodationRepository.GetByIdAsync(id);
    }

    public async Task<Accommodation> CreateAccommodationAsync(Accommodation accommodation)
    {
        // Set timestamps for new entity
        accommodation.CreatedAt = DateTime.UtcNow;
        accommodation.UpdatedAt = DateTime.UtcNow;

        // AddAsync returns void and handles SaveChanges internally
        await _accommodationRepository.AddAsync(accommodation);

        // Return the accommodation with its generated ID
        return accommodation;
    }

    public async Task<Accommodation> UpdateAccommodationAsync(Accommodation accommodation)
    {
        // Check if accommodation exists first using ExistsAsync to avoid tracking
        var exists = await _accommodationRepository.ExistsAsync(accommodation.Id);
        if (!exists)
        {
            throw new InvalidOperationException($"Accommodation with ID {accommodation.Id} not found.");
        }

        // Update timestamp
        accommodation.UpdatedAt = DateTime.UtcNow;

        // UpdateAsync returns void and handles SaveChanges internally
        await _accommodationRepository.UpdateAsync(accommodation);

        return accommodation;
    }

    public async Task<bool> DeleteAccommodationAsync(Int64 id)
    {
        // Check if accommodation exists first using ExistsAsync to avoid tracking
        var exists = await _accommodationRepository.ExistsAsync(id);
        if (!exists)
        {
            return false;
        }

        // DeleteAsync returns void and handles SaveChanges internally
        await _accommodationRepository.DeleteAsync(id);

        return true;
    }

    public async Task<bool> AccommodationExistsAsync(Int64 id)
    {
        return await _accommodationRepository.ExistsAsync(id);
    }
}
