using EAMS.Domain.Entities;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Exceptions;

namespace EAMS.Domain.Services;

public class OrganisationService : IOrganisationService
{
    private readonly IOrganisationRepository _organisationRepository;

    public OrganisationService(IOrganisationRepository organisationRepository)
    {
        _organisationRepository = organisationRepository;
    }

    public async Task<IEnumerable<Organisation>> GetAllOrganisationsAsync()
    {
        return await _organisationRepository.GetAllAsync();
    }

    public async Task<Organisation?> GetOrganisationByIdAsync(Guid id)
    {
        return await _organisationRepository.GetByIdAsync(id);
    }

    public async Task<Organisation> CreateOrganisationAsync(Organisation organisation)
    {
        // Set timestamps for new entity
        organisation.CreatedAt = DateTime.UtcNow;
        organisation.UpdatedAt = DateTime.UtcNow;

        // AddAsync returns void and handles SaveChanges internally
        await _organisationRepository.AddAsync(organisation);

        // Return the organisation with its generated ID
        return organisation;
    }

    public async Task<Organisation> UpdateOrganisationAsync(Organisation organisation)
    {
        // Check if organisation exists first using ExistsAsync to avoid tracking
        var exists = await _organisationRepository.ExistsAsync(organisation.Id);
        if (!exists)
        {
            throw new EntityNotFoundException("Organisation", organisation.Id);
        }

        // Update timestamp
        organisation.UpdatedAt = DateTime.UtcNow;

        // UpdateAsync returns void and handles SaveChanges internally
        await _organisationRepository.UpdateAsync(organisation);

        return organisation;
    }

    public async Task<bool> DeleteOrganisationAsync(Guid id)
    {
        // Check if organisation exists first using ExistsAsync to avoid tracking
        var exists = await _organisationRepository.ExistsAsync(id);
        if (!exists)
        {
            return false;
        }

        // DeleteAsync returns void and handles SaveChanges internally
        await _organisationRepository.DeleteAsync(id);

        return true;
    }

    public async Task<bool> OrganisationExistsAsync(Guid id)
    {
        return await _organisationRepository.ExistsAsync(id);
    }
}
